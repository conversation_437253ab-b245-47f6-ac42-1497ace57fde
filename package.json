{"name": "salestool", "version": "0.1.1", "private": true, "homepage": "/salestool", "dependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.6", "@testing-library/user-event": "^14.5.2", "axios": "^1.6.8", "bootstrap": "^5.3.3", "firebase": "^10.11.1", "flowbite": "^2.3.0", "flowbite-react": "^0.9.0", "jquery": "^3.7.1", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-error-overlay": "^6.0.11", "react-owl-carousel": "^2.3.3", "react-redux": "^9.1.2", "react-router-dom": "^6.23.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-star-ratings": "^2.3.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "simple-peer": "^9.7.2", "socket.io": "^2.3.0", "web-vitals": "^3.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"react-error-overlay": "6.0.9"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "webpack": "^5.91.0", "webpack-dev-server": "^5.0.4"}}