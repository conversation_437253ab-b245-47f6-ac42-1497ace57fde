import React from "react"
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction"
import VideoCard from "./VideoCard";
import * as ExceptionActions from "../../../Actions/Exception"
import Draggable from 'react-draggable'; // The default
import Share from "../../../Tools/ToolComponents/ShareModal";
import Firebase from "../../../config/Firebase";
import pin from '../../../assets/pin.svg';
import unpin from '../../../assets/unpin.svg';

class SideBar extends React.Component {
  constructor(props) {
    super(props);

    this.createUserVideo = this.createUserVideo.bind(this);
    this.localvideo = React.createRef();
    this.sendmessage = this.sendmessage.bind(this);
    this.MuteAll = this.MuteAll.bind(this);
    this.MuteGuest = this.MuteGuest.bind(this);
    this.inputFileRef = React.createRef();
    this.state = {
      currentPosition: null,
      drag: false,
      currentX: null,
      currentY: null,
      initialX: null,
      initialY: null,
      xOffset: 0,
      yOffset: 0,
      isContentVisible: true,
      isHovered: false,
      isMaximized: false,
    };

  }

  // for dragable video card

  shouldComponentUpdate(nextProps, nextState) {
    if(nextProps.Tab!=this.props.Tab){
      if (nextProps.Tab) {
        const draggable = document.getElementById("LocalVideo");
        this.setState({
          currentPosition:draggable.parentElement.style.transform
        })
        // this.state.currentPosition = draggable.parentElement.style.transform;
        draggable.parentElement.style.transform = "translate(0px, 0px)";
      } else {
        if (this.state.currentPosition) {
          const draggable = document.getElementById("LocalVideo");
          draggable.parentElement.style.transform = this.state.currentPosition;
        }


      }
    }

    return true
  }

  componentDidMount() {
    window.addEventListener('resize', this.handleResize);
    this.handleResize();


  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.handleResize);
  }

  handleResize = () => {
    const isMobile = window.innerWidth <= 768;
    this.setState({ isMobile });
  };

  handleVideoDragStart = (event) => {
    event.preventDefault();



  };

  handleVideoDrag = (e) => {
    e.preventDefault();
    const draggable = document.getElementById("LocalVideo");



    draggable.addEventListener("mousedown", (event) => {

      this.state.initialX = event.clientX - this.state.xOffset;
      this.state.initialY = event.clientY - this.state.yOffset;
      if (event.target === draggable) {
        // this.setState({drag:true})
        this.state.drag = true;
      }

      document.addEventListener("mousemove", (event) => {
        if (this.state.drag) {
          event.preventDefault();
          // if((this.state.initialX - event.clientX )>0)
          this.state.currentX = event.clientX - this.state.initialX;
          // if((event.currentY - this.state.initialY)>0)
          this.state.currentY = event.clientY - this.state.initialY;

          this.state.xOffset = this.state.currentX;
          this.state.yOffset = this.state.currentY;
          window.requestAnimationFrame(() => {
            draggable.parentElement.style.transform = "translate3d(" + this.state.currentX + "px, " + this.state.currentY + "px, 0)";
            // draggable.parentElement.style.top = this.state.currentX + "px";
            // draggable.parentElement.style.left = this.state.currentY + "px";

          })
        }
      });

      document.addEventListener("mouseup", (event) => {
        this.state.initialX = this.state.currentX;
        this.state.initialY = this.state.currentY;

        // this.setState({drag:false})
        this.state.drag = false
      });
    });
  };

  handleVideoDragStart = (e) => {
    e.preventDefault();
    const { clientX, clientY } = this.getTouchEventCoordinates(e);
    this.setState({ dragStart: { x: clientX, y: clientY } });
  };

  handleVideoDragEnd = () => {
    this.setState({ dragStart: null });
  };

  getTouchEventCoordinates = (e) => {
    if (e.touches && e.touches.length) {
      return { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY };
    }
    return { clientX: e.clientX, clientY: e.clientY };
  };

  // //////////////////////////////////////////////////////////////////
  componentDidMount() {
    HostActions.SubscribeToChat((msg) => {
      var data = JSON.parse(msg);
      if (this.props.Tab !== "CHAT") {
        this.props.SetUnreadMessage(this.props.UnreadCount + 1);
      }
      this.props.SetMessage(data)
    })
  }

  handleToggleContent = () => {
    this.setState((prevState) => ({ isContentVisible: !prevState.isContentVisible }));
  };


  sendmessage(e) {
    e.preventDefault();
    const message = {
      actiontype: "chat",
      room: this.props.roomId,
      message: document.getElementById("messagearea").value,
      name: "Host",
      type: "message"
    };
    this.props.SendMessage(message, this.props.roomId);
    setTimeout(() => {
      var element = document.getElementById("chat_bar");
      if (element)
        element.scrollTop = element.scrollHeight;
    }, 500);


    document.getElementById("messagearea").value = "";
  }
  fileupload = (event) => {
    const validFormats = [".jpg", ".jpeg", ".png", ".pdf", ".docx", ".xlsx", ".pptx", ".txt"];
    let file = event.target.files[0];
    let reader = new FileReader();
    reader.readAsDataURL(file);
    const fileExt = file.name.slice(file.name.lastIndexOf("."));
    if (!validFormats.includes(fileExt)) {
      this.props.CreateToast({ message: "File Format is not supported" });
      return
    }
    reader.onloadend = () => {
      const message = {
        actiontype: "chat",
        message: "",
        room: this.props.roomId,
        type: "file",
        name: "Host",
        filename: file.name,
        filedata: reader.result
      };
      this.props.SendMessage(message, this.props.roomId);


    };
  }
  urlify(text) {
    var urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.replace(urlRegex, function (url) {
      return '<a class="chat_link" target="_blank" href="' + url + '">' + url + '</a>';
    })
  }
  createUserVideo(peer) {

    return (
      <li className="video_content">

        <VideoCard MuteGuest={this.MuteGuest} peer={peer} />
      </li>
    );
  }

  open_close = (name, flag) => {
    // document.getElementById('tools_div').classList.remove('show');

    this.setState({
      [name]: flag
    })

    if (name === 'map') {
      if (flag) {
        this.props.senddata({ actiontype: "map", data: this.props.data["latlng"] });
        this.setState({
          mapdata: this.props.data["latlng"],
          map: true
        })

      }
      else {
        this.props.senddata({ actiontype: "map", data: "false" });
        this.setState({
          mapdata: false,
          map: true
        })
      }
    }


    // this.menu_bar_open();
    // if (!flag) {
    //   this.props.senddata({ actiontype: "floorplan", roomid: this.props.roomId, data: false, pin: null })
    // }


    // this.setState({
    //   [name]:flag
    // })
  }

  MuteAll(e) {
    if (e.currentTarget.checked) {
      this.props.MuteAll(this.props.roomId, true);
    } else {
      this.props.MuteAll(this.props.roomId, false);
    }
  }
  MuteGuest(id, status) {
    this.props.Mute(id, this.props.roomId, status)
  }

  handleMouseEnter = () => {
    this.setState({ isHovered: true });
    console.log('enter Hovered: ', this.state.isHovered);
  };

  handleMouseLeave = () => {
    this.setState({ isHovered: false });
    console.log('out Hovered: ', this.state.isHovered);
  };

  handleMaximizeMembersVideo = () => {
    this.setState({ isMaximized: !this.state.isMaximized });
  };
  handleTouchStart = () => {
    this.setState({ isHovered: true });
  };

  handleTouchEnd = () => {
    this.setState({ isHovered: false });
  };

  render() {
    const { isMobile } = this.state;
    const { isContentVisible } = this.state;
    const { Tab } = this.props;
    const { isHovered } = this.state;
    const { isMaximized } = this.state;
    return (<>
      <>
        <div id="mySidenav" className={`${this.props.Tab ? "backdrop-blur-[6px] w-[100%]  right-[0px]  md:w-[50%] lg:w-[20%]" : "w-[0px] right-[-320px]"}  h-[100%]   z-[9999]  transition-[right] duration-[0.5s] ease-[ease-in-out]  shadow-[0px_17px_21px_0px_#0000000D] top-0  bg-[#00000080]`}
          style={{ marginLeft: this.props.Tab ? "0px" : "0px", position: this.props.Tab ? 'fixed' : 'relative', display: (this.props.ShowControls ? "block" : "none") }} ref={this.props.Sidenav} >
          {/* <button datasrc="close_icon" style={{ display: this.state.tab ? "block" : "none" }} onClick={this.togglenav} className="closebtn">×</button> */}
          <div className="absolute flex justify-between w-[100%] right-0 top-0 p-[10px]">
            {this.props.Tab === "CHAT" && (
              <div className="text-base font-semibold leading-6 tracking-normal text-white text-left">
                Chats
              </div>
            )}
            {this.props.Tab === "MEMBERS" && (
              <div className="text-base font-semibold leading-6 tracking-normal text-white text-left">
                People
              </div>
            )}
            <div onClick={() => { this.props.TabControl(false) }}>  <svg width="24" height="24" viewBox="0 0 24 24"><defs><path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z"></path></defs><g fill="#ffffff" fill-rule="evenodd" transform="translate(6 6)"><use fill="#ffffff" href="#prefix__close"></use></g></svg></div>
          </div>
          <div style={{ height: '94.5%', display: "flex", flexDirection: "column", position: 'relative', top: '50px' }}>
            <div className="tab-content text-center" style={{ height: '100%', overflow: 'hidden', paddingBottom: '20px' }}>
              <div className="tab-pane active show" id="members" style={{ height: '100%', display: this.props.Tab != "CHAT" ? "block" : "none" }}>
                <div onClick={() => this.open_close('share', true)} name="share" className="flex justify-center bg-[#1C74D0] gap-[.2rem] w-[78%] text-[white] m-auto p-2">
                  <svg className="w-5 h-5" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14.5 5C14.5 5.90058 14.2778 6.73684 13.8333 7.50877C13.3889 8.269 12.7807 8.8772 12.0088 9.3333C11.2485 9.7778 10.4123 10 9.5 10C8.59942 10 7.76316 9.7778 6.99123 9.3333C6.23099 8.8772 5.62281 8.269 5.16667 7.50877C4.72222 6.74854 4.5 5.91228 4.5 5C4.5 4.08772 4.72222 3.25146 5.16667 2.49123C5.62281 1.7193 6.23099 1.11111 6.99123 0.66667C7.75146 0.22222 8.58772 0 9.5 0C10.4123 0 11.2485 0.22222 12.0088 0.66667C12.7807 1.11111 13.3889 1.7193 13.8333 2.49123C14.2778 3.25146 14.5 4.08772 14.5 5ZM9.5 11C11.1023 11 12.5877 11.2105 13.9561 11.6316C15.3363 12.0526 16.4357 12.6433 17.2544 13.4035C18.0848 14.1637 18.5 15.0292 18.5 16C18.5 16.7135 18.3187 17.3801 17.9561 18C17.6053 18.6082 17.1199 19.0936 16.5 19.4561C15.8918 19.8187 15.2251 20 14.5 20H4.5C3.77485 20 3.10819 19.8187 2.5 19.4561C1.89181 19.0936 1.40643 18.6082 1.04386 18C0.68129 17.3918 0.5 16.7251 0.5 16C0.5 15.0292 0.90936 14.1637 1.72807 13.4035C2.55848 12.6433 3.65789 12.0526 5.02632 11.6316C6.40643 11.2105 7.89766 11 9.5 11ZM17.5 6C17.5 5.7193 17.5994 5.48538 17.7982 5.29824C17.9971 5.09942 18.231 5 18.5 5C18.7807 5 19.0146 5.09942 19.2018 5.29824C19.4006 5.48538 19.5 5.7193 19.5 6V12C19.5 12.269 19.4006 12.5029 19.2018 12.7018C19.0146 12.9006 18.7807 13 18.5 13C18.231 13 17.9971 12.9006 17.7982 12.7018C17.5994 12.5029 17.5 12.269 17.5 12V6ZM15.5 10C15.231 10 14.9971 9.9006 14.7982 9.7018C14.5994 9.5029 14.5 9.269 14.5 9C14.5 8.7193 14.5994 8.4854 14.7982 8.2982C14.9971 8.0994 15.231 8 15.5 8H21.5C21.7807 8 22.0146 8.0994 22.2018 8.2982C22.4006 8.4854 22.5 8.7193 22.5 9C22.5 9.269 22.4006 9.5029 22.2018 9.7018C22.0146 9.9006 21.7807 10 21.5 10H15.5Z" fill="white" />
                  </svg>
                  <button>Invite Members</button>
                </div>
                {/* {this.props.Tab ? <div style={{ float: "right", color: '#A3A3A3', marginRight: "8px",marginTop:"-20px" }}><input style={{ marginRight: "8px",backgroundColor:'transparent' }} type="checkbox" onClick={this.MuteAll} />Mute all</div> : ""} */}
                <ul className= "overflow-auto p-2" style={{ height: '90%', margin: "0px", listStyle: 'none', width: '100%', padding: "0px" }}>
                  <li className="video_content">
                    <Draggable
                      bounds={{ top: -50, bottom: (window.innerHeight - 50), right: (window.innerWidth - 100), left: -100 }}
                      disabled={this.props.Tab ? true : false}
                      defaultPosition={{x: 0, y: 50}}
                  >

                      <div ref={this.localvideo} style={{ display: !isContentVisible && !this.props.Tab ? "none" : "block", top: !this.props.Tab && isMaximized ? "3.1rem" : "0px" }} id="relative-localvideo" className={"aspect-video " + (this.props.Tab || !this.state.isContentVisible ? "custom-aspect-ratio fixed-video" : "relative-localvideo")}  >
                        {isHovered ?(
                          <button onClick={this.handleMaximizeMembersVideo} style={{ display: 'block', position: 'absolute', zIndex: '99999' }} className="relative backdrop-blur-[6px]  cursor-pointer right-[0px] border-[none] shadow-[0px_17px_21px_0px_#0000000D] p-2 rounded-full bg-[black]/10 bg-opacity-10">
                            {!isMaximized ? <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z" />  <path d="M4 8v-2a2 2 0 0 1 2 -2h2" />  <path d="M4 16v2a2 2 0 0 0 2 2h2" />  <path d="M16 4h2a2 2 0 0 1 2 2v2" />  <path d="M16 20h2a2 2 0 0 0 2 -2v-2" /></svg> :
                            <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">  <path stroke="none" d="M0 0h24v24H0z" />  <path d="M15 19v-2a2 2 0 0 1 2 -2h2" />  <path d="M15 5v2a2 2 0 0 0 2 2h2" />  <path d="M5 15h2a2 2 0 0 1 2 2v2" />  <path d="M5 9h2a2 2 0 0 0 2 -2v-2" /></svg>}
                          </button>
                        ):null}
                        <div className="VideoControls_Div m-1"  >
                          <div className=" w-[100%] flex justify-between p-1 text-white relative" >
                            <button onClick={this.handleToggleContent}  className="p-1 rounded-[50%] bg-[black]/20 bg-opacity-20 backdrop-blur-[6px]">
                              {isContentVisible ? (
                                <img src={unpin} className="w-4 h-4" alt="unpin" title='Unpin Member' />
                              ) : (
                                  <img src={pin} className="w-4 h-4"  alt="pin" title='Pin Member' />
                              )}
                            </button>
                            <div style={{marginTop:'auto'}}> You</div>
                          </div>
                        </div>
                        <video
                          className="user-video"
                          id="LocalVideo"
                          autoPlay
                          muted
                          playsInline
                          style={{...(!this.props.Tab && isMaximized ? {position:'relative',height:'77vh',width:'100vw'} : null)}}
                        // style={{
                        //   ...(this.props.Tab || isMobile ? { top: '0px', left: '0px' } : {
                        //     top: `${this.state.videoPosition.y}px`,
                        //     left: `${this.state.videoPosition.x}px`,
                        //   })
                        // }}
                        // onMouseMove={this.handleVideoDrag}
                        // onMouseUp={this.handleVideoDragEnd}
                        // onMouseLeave={this.handleVideoDragEnd}
                        //onTouchStart={this.handleVideoDragStart}
                        // // onTouchMove={this.handleVideoDrag}
                        // onTouchEnd={this.handleVideoDragEnd}
                        />
                      </div>
                    </Draggable>
                  </li>
                  {this.props.Peers &&
                    Object.values(this.props.Peers).map((peer) => { if (peer != null) { return this.createUserVideo(peer) } return "" })}


                </ul>
              </div>
              {this.props.Tab == "CHAT" ? <div className="tab-pane" id="chat" style={{ height: '100%', display: "flex", flexDirection: 'column' }}>
                <ul className="chat_bar" id="chat_bar" >

                  {this.props.Messages.map((child, key) => {

                    if (child.type === "message") {
                      return (

                        key < 1 ?

                          <li className={this.props.socketid === child.user ? "self" : "other"} ><div className="chat_name">{child.name}</div> <div className={this.props.socketid === child.user ? "self_msg" : "other_msg"} dangerouslySetInnerHTML={{ __html: this.urlify(child.message) }}></div>
                          </li>
                          :

                          this.props.Messages[key].user === this.props.Messages[key - 1].user ?
                            <li className={this.props.socketid === child.user ? "self" : "other"} style={{ "marginTop": "0px" }}> <div className={this.props.socketid === child.user ? "self_msg" : "other_msg"} dangerouslySetInnerHTML={{ __html: this.urlify(child.message) }}></div>
                            </li>


                            : <li className={this.props.socketid === child.user ? "self" : "other"} ><div className="chat_name">{child.name}</div> <div className={this.props.socketid === child.user ? "self_msg" : "other_msg"} dangerouslySetInnerHTML={{ __html: this.urlify(child.message) }}></div>
                            </li>




                      )
                    }
                    else {
                      return (
                        <li className={this.props.socketid === child.user ? "self" : "other"} >
                          {key < 1 ? <div className="chat_name">{child.name}</div> :
                            this.props.Messages[key].user === this.props.Messages[key - 1].user ? <></> : <div className="chat_name">{child.name}</div>

                          }
                          <div className={this.props.socketid === child.user ? " media_msg self_msg" : "media_msg  other_msg"}><span className="media_file_name">{child.filename}</span>
                            <span style={{ paddingRight: '8px', cursor: 'pointer' }}>
                              <a target="_blank" rel="noopener noreferrer" href={child.filedata} download={child.filename}> <svg width={24} height={24} viewBox="0 0 24 24">
                                <defs>
                                  <path id="prefix__download" d="M19 16c.55 0 1 .45 1 1v2c0 .51-.388.935-.884.993L19 20H5c-.55 0-1-.45-1-1v-2c0-.55.45-1 1-1s1 .45 1 1v1h12v-1c0-.55.45-1 1-1zM12 3c.553 0 1 .448 1 1v8l2.4-1.8c.442-.333 1.069-.242 1.4.2.332.442.242 1.069-.2 1.4l-4 3c-.177.133-.389.2-.6.2-.201 0-.402-.061-.575-.182l-4-2.814c-.452-.318-.561-.942-.243-1.393.318-.452.941-.561 1.393-.243l2.428 1.71L11 12V4c0-.552.447-1 1-1z" />
                                </defs>
                                <g fill="none" fillRule="evenodd">
                                  <use fill="#fff" xlinkHref="#prefix__download" />
                                </g>
                              </svg></a>
                            </span>
                          </div>
                        </li>

                      )
                    }
                  })}

                </ul>
                <div style={{ width: '100%', padding: '0 10px' }}>
                  <form className="  rounded flex h-10 relative w-full -mt-9 p-2.5 border border-solid border-[white]" onSubmit={this.sendmessage} style={{ marginBottom: '0px' }}><span style={{ cursor: 'pointer' }}><input ref={this.inputFileRef} onChange={this.fileupload} type="file" accept=".jpg, .jpeg, .png, .pdf, .docx, .xlsx, .pptx, .txt" style={{ display: 'none' }} />
                    <div onClick={() => { this.inputFileRef.current.click(); }}><svg width="20" height="22" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6.50053 21.5531C5.30426 21.5506 4.13549 21.1941 3.14147 20.5285C2.14745 19.8629 1.37266 18.9181 0.914725 17.8129C0.456785 16.7078 0.336181 15.4918 0.56811 14.3182C0.800038 13.1446 1.37412 12.066 2.21803 11.2181L11.593 1.84309C12.4588 1.1694 13.5408 0.835163 14.6357 0.9032C15.7306 0.971238 16.7629 1.43687 17.5386 2.21256C18.3143 2.98824 18.7799 4.02056 18.8479 5.11544C18.916 6.21031 18.5817 7.29234 17.908 8.15809L9.28303 16.7831C8.74398 17.3221 8.01287 17.625 7.25053 17.625C6.48819 17.625 5.75708 17.3221 5.21803 16.7831C4.67898 16.244 4.37614 15.5129 4.37614 14.7506C4.37614 13.9883 4.67898 13.2571 5.21803 12.7181L10.468 7.46809C10.6093 7.32686 10.8008 7.24752 11.0005 7.24752C11.2003 7.24752 11.3918 7.32686 11.533 7.46809C11.6743 7.60932 11.7536 7.80086 11.7536 8.00059C11.7536 8.20032 11.6743 8.39186 11.533 8.53309L6.28303 13.7831C6.02643 14.0397 5.88228 14.3877 5.88228 14.7506C5.88228 15.1135 6.02643 15.4615 6.28303 15.7181C6.53963 15.9747 6.88765 16.1188 7.25053 16.1188C7.61341 16.1188 7.96143 15.9747 8.21803 15.7181L16.843 7.09309C17.3558 6.52986 17.6319 5.79079 17.614 5.02934C17.5961 4.26789 17.2857 3.54258 16.7471 3.004C16.2085 2.46543 15.4832 2.15498 14.7218 2.13712C13.9603 2.11926 13.2213 2.39536 12.658 2.90809L3.28303 12.2831C2.83834 12.7006 2.48208 13.2031 2.23539 13.7609C1.9887 14.3188 1.85663 14.9205 1.847 15.5303C1.83737 16.1402 1.9504 16.7458 2.17935 17.3111C2.40831 17.8764 2.74854 18.39 3.17983 18.8213C3.61112 19.2526 4.12468 19.5928 4.69001 19.8218C5.25534 20.0507 5.86092 20.1637 6.47078 20.1541C7.08064 20.1445 7.68235 20.0124 8.24018 19.7657C8.798 19.519 9.30057 19.1628 9.71803 18.7181L17.968 10.4681C18.1093 10.3269 18.3008 10.2475 18.5005 10.2475C18.7003 10.2475 18.8918 10.3269 19.033 10.4681C19.1743 10.6093 19.2536 10.8009 19.2536 11.0006C19.2536 11.2003 19.1743 11.3919 19.033 11.5331L10.783 19.7831C10.2222 20.3473 9.55475 20.7943 8.81955 21.0982C8.08434 21.4021 7.29605 21.5567 6.50053 21.5531Z" fill="white" />
                    </svg></div>
                  </span><input id="messagearea" type="text" className="w-[80%] bg-[transparent] rounded text-left text-[white] not-italic font-semibold text-[white] text-[15px] leading-6 pl-2.5 " placeholder="Send message" required />
                    <button type="submit" style={{ cursor: 'pointer', backgroundColor: 'transparent', border: 'none', color: 'white' }}>
                      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M17.1686 6.83145L9.24852 12.3303L0.964497 9.5686C0.386258 9.37546 -0.00330584 8.83294 2.11451e-05 8.22344C0.00339191 7.61395 0.397421 7.07476 0.977892 6.88836L22.1573 0.0678197C22.6607 -0.0940207 23.2133 0.0387961 23.5873 0.412776C23.9612 0.786755 24.0941 1.3393 23.9322 1.84277L17.1117 23.0221C16.9253 23.6026 16.3861 23.9966 15.7766 24C15.1671 24.0033 14.6246 23.6138 14.4314 23.0355L11.6563 14.7114L17.1686 6.83145Z" fill="white" />
                      </svg>
                    </button>
                  </form>
                </div>
              </div> : ""}
            </div></div>
        </div>


        <Share open_close={this.open_close} share={this.state.share} pid={this.props.roompid} roomId={this.props.roomId} user_id={Firebase.auth().currentUser.uid}  ></Share>
      </>
    </>
    )
  }
}

const mapStateTothisprops = state => {
return {
    Peers: state.Call.peers,
    VideoDevices: state.Call.VideoDevices,
    SocketId: state.Call.SocketId,
    LocalStream: state.Call.LocalStream,
    Messages: state.Call.Messages,
    Tab: state.Call.Tab,
    UnreadCount: state.Call.UnreadCount,
    ShowControls: state.Call.ShowControls,
  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(SideBar)