import React from "react";
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction"
import VideoStream from "../../../components/VideoStream";



class JoiningPageHost extends React.Component{
constructor(props){
    super(props);
    this.HandleVideo=this.HandleVideo.bind(this);
    this.HandleAudio=this.HandleAudio.bind(this);
    this.state={
        video:true,
        audio:true
    }
}
HandleVideo(){

if(this.props.Video){
this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
this.props.LocalStream.addTrack(HostActions.canvastrack)
this.props.ToogleLocalAV("Video",false)
}
else{
navigator.mediaDevices.getUserMedia({video:true}).then(stream=>{
this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
this.props.LocalStream.addTrack(stream.getVideoTracks()[0]);
this.props.ToogleLocalAV("Video",true)
})
}
}
HandleAudio(){
  if(this.props.Audio){
    this.props.LocalStream.getAudioTracks()[0].enabled=false;
    this.props.ToogleLocalAV("Audio",false)
    }
    else{

      this.props.LocalStream.getAudioTracks()[0].enabled=true;
      this.props.ToogleLocalAV("Audio",true)
  
    }
}
Strength(position,network){
  switch (network.strength) {
    case "WEEK":
      if(position<=1){
        return "red"
      }else{
        return "#ff00006e"
      }
      case "MODERATE":
        if(position<=2){
          return "#f57a06"
        }else{
          return "#f57a068f"
        }
      case "BETTER":
        if(position<=3){
          return "#3fff08"
        }else{
          return "#3fff08"
        }
        case "EXCELLENT":
        if(position<=3){
          return "#3fff08"
        }else{
          return "#3fff08"
        }
    default:
      return "#fff"
  }
}
    render(){
        return(
            <>
            
            <div className="cd-section">
   <div className="row main_video_row" style={{width:"fit-content"}}>
      <div className="col-md-6 INITVIDEO" style={{margin: 'auto',height:"310px",background:"#000",width:"500px"}}>
          {this.props.LocalStream && this.props.Video?<VideoStream   stream={this.props.LocalStream}/>:<div style={{height:"inherit",background:"#000"}}></div> }
      <div style={{ transform: 'translateY(-64px)',display:"flex",flexDirection:"row"}}>
      <div className=" microphone_div" style={{margin:"auto"}}>
         <div onClick={this.HandleAudio} className="video_icon_div" style={{background:this.props.Audio?"":"red"}}>
        {this.props.Audio? <svg  width={24} height={24} viewBox="0 0 24 24">
                <path fill="#fff" fillRule="evenodd" d="M13 17.92V20h2.105c.493 0 .895.402.895.895v.21c0 .493-.402.895-.895.895h-6.21C8.402 22 8 21.598 8 21.106v-.211c0-.493.402-.895.895-.895H11v-2.08c-3.387-.488-6-3.4-6-6.92 0-.552.447-1 1-1 .553 0 1 .448 1 1 0 2.757 2.243 5 5 5s5-2.243 5-5c0-.552.447-1 1-1 .553 0 1 .448 1 1 0 3.52-2.613 6.432-6 6.92zM10 6c0-1.103.897-2 2-2s2 .897 2 2v5c0 1.103-.897 2-2 2s-2-.897-2-2V6zm2 9c2.206 0 4-1.794 4-4V6c0-2.205-1.794-4-4-4S8 3.795 8 6v5c0 2.206 1.794 4 4 4z" />
              </svg>:<svg width={24} height={24}  viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                  <g data-name="mic-off">
                  <rect width="24" height="24" opacity="0"/>
                  <path fill="#fff" d="M10 6a2 2 0 0 1 4 0v5a1 1 0 0 1 0 .16l1.6 1.59A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75L10 7.17z"/>
                  <path fill="#fff" d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z"/>
                  <path fill="#fff" d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z"/>
                  <path fill="#fff" d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/>
                  <path fill="#fff" d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z"/>
                  </g>
                  </g>
                  </svg>}
         </div>
      </div>
      <div style={{margin:"auto"}}>
      <div onClick={this.HandleVideo} className="video_icon_div" style={{background:this.props.Video?"":"red"}}>
         {this.props.Video?
         <svg id="video_on"  width={24} height={24} viewBox="0 0 24 24">
         <defs>
            <path id="prefix__z" d="M18 9.6L15.191 7 18 4.4v5.2zM13 11c0 .552-.448 1-1 1H3c-.552 0-1-.448-1-1V3c0-.551.448-1 1-1h9c.552 0 1 .449 1 1v8zm6.012-8.854c-.626-.273-1.352-.154-1.851.306l-2.161 2V3c0-1.654-1.346-3-3-3H3C1.346 0 0 1.346 0 3v8c0 1.655 1.346 3 3 3h9c1.654 0 3-1.345 3-3V9.549l2.161 1.999c.32.297.735.452 1.158.452.234 0 .469-.047.693-.145.609-.266.988-.835.988-1.484V3.63c0-.65-.379-1.218-.988-1.484z" />
          </defs>
          <g fill="none" fillRule="evenodd" transform="translate(2 5)">
            <use fill="#fff" xlinkHref="#prefix__z" />
          </g>
        </svg>:<svg id="video_off"  width={24} height={24} viewBox="0 0 24 24">
          <path fill="#fff" fillRule="evenodd" d="M20 9.4L17.191 12 20 14.6V9.4zm2-.77v6.74c0 .65-.379 1.218-.988 1.484-.224.098-.459.146-.693.146-.206 0-.409-.038-.601-.11L15 12.17v-4.17c0-.553-.448-1-1-1H9.828l-2-2H14c1.654 0 3 1.344 3 3v1.45l2.161-2c.499-.46 1.225-.578 1.851-.306.609.266.988.835.988 1.484zM14 17H5c-.552 0-1-.449-1-1V8c0-.32.161-.593.396-.777L2.974 5.801C2.379 6.351 2 7.13 2 8.001v8c0 1.653 1.346 3 3 3h9c.616 0 1.188-.189 1.665-.508l-1.522-1.523c-.049.008-.092.03-.143.03zm6.707 2.293c.391.39.391 1.023 0 1.414-.195.195-.451.293-.707.293-.256 0-.512-.098-.707-.293L16.386 17.8l-1.455-1.455L5.586 7l-1.76-1.76-.533-.533c-.391-.39-.391-1.024 0-1.414.391-.39 1.023-.39 1.414 0L6.414 5l2 2L15 13.586l2 2 3.707 3.707z" />
        </svg>}
      </div>
   </div>
   <div style={{margin:"auto"}}>
      <div  className="video_icon_div" >
   <svg fill="#fff" width={24} height={24} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
   <g data-name="Layer 2">
      <g data-name="wifi">
         <rect width={24} height={24} opacity={0} />
         <circle fill={this.Strength(0,this.props.NetWorkSpeed)}  cx={12} cy={19} r={1} />
         <path fill={this.Strength(1,this.props.NetWorkSpeed)} d="M12 14a5 5 0 0 0-3.47 1.4 1 1 0 1 0 1.39 1.44 3.08 3.08 0 0 1 4.16 0 1 1 0 1 0 1.39-1.44A5 5 0 0 0 12 14z" />
         <path fill={this.Strength(2,this.props.NetWorkSpeed)} d="M12 9a9 9 0 0 0-6.47 2.75A1 1 0 0 0 7 13.14a7 7 0 0 1 10.08 0 1 1 0 0 0 .71.3 1 1 0 0 0 .72-1.69A9 9 0 0 0 12 9z" />
         <path fill={this.Strength(3,this.props.NetWorkSpeed)} d="M21.72 7.93a14 14 0 0 0-19.44 0 1 1 0 0 0 1.38 1.44 12 12 0 0 1 16.68 0 1 1 0 0 0 .69.28 1 1 0 0 0 .72-.31 1 1 0 0 0-.03-1.41z" />
      </g>
   </g>
</svg>
</div>
</div>

</div>
</div>
<div className="col-md-6" style={{margin: 'auto'}}>
<div className="row" style={{margin: 0, justifyContent: 'center'}}>
<div className="col-md-12 text-center video_heading">
   Ready to join?
</div>
<div className="col-md-12 text-center video_subheading">
   No one else is here
</div>
</div>
<div onClick={this.props.Join} className="row" style={{margin: 0, justifyContent: 'center'}}>
<span className="join_now_video">
<span  style={{display: 'flex', alignItems: 'center'}}>Join now</span>
</span>
</div>
</div>
</div>
</div></>
        )
    }
}

const mapStateToProps = state => {
    return {
      LocalStream:state.Call.LocalStream,
      Video:state.Call.Video,
      Audio:state.Call.Audio,
      NetWorkSpeed:state.Call.NetWorkSpeed
    }
  }
  
  const mapDispatchToProps = {
    ...HostActions
  }
  
  export default connect(mapStateToProps, mapDispatchToProps)(JoiningPageHost)