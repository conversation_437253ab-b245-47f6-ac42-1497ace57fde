import React from 'react';
import { connect } from 'react-redux';
import Wave from '../../../Tools/ToolComponents/AudioVis';
import * as HostActions from '../../../Actions/HostAction';

class VideoCard extends React.Component {
  constructor(props) {
    super(props);
    this.ref = React.createRef();
    this.state = {
      isScreenSharingMinimized: false,
    };
  }

  componentDidMount() {
    if (this.props.peer.extra.type === 'host') {
      this.props.SetHostStatus(true);
    }

    let wave = new Wave();
    const peer = this.props.peer;

    peer.on('stream', (stream) => {
      this.props.ConnectMUX(stream);

      if (
        this.props.userVideoAudio[this.props.peer.peerID] &&
        this.props.peer.extra.type === 'host'
      ) {
        // this.ref.current.requestPictureInPicture()
      }

      this.ref.current.srcObject = stream;

      wave.fromStream(stream, peer.peerID, {
        colors: ['#ffffff00', '#2979FF'],
      });
    });

    peer.on('track', (track, stream) => { });
  }

  shouldComponentUpdate(nextProps, nextState) {
    return true;
  }

  handleMinimizeClick = () => {
    this.setState((prevState) => ({
      isScreenSharingMinimized: !prevState.isScreenSharingMinimized,
    }));
  };

  render() {
    const isScreenSharing =
      this.props.userVideoAudio[this.props.peer.peerID] &&
      this.props.userVideoAudio[this.props.peer.peerID].screen;

    const { isScreenSharingMinimized } = this.state;

    return (
      <>
        <div
          className={
            (this.props.userVideoAudio[this.props.peer.peerID]
              ?  this.props.userVideoAudio[this.props.peer.peerID].screen
                  ? 'fixed top-0 left-0'
                  : 'fixed-video custom-aspect-ratio aspect-video '
                : "fixed-video custom-aspect-ratio aspect-video ")+" "
          }
        >
          <div className="VideoControls_Div">
            <div
              className="VideoOffname"
              style={{ display: isScreenSharing && isScreenSharingMinimized && !this.props.tab ? "none" : "" , marginRight: '8px', textAlign: 'end' }}
            >
              <div
                style={{
                  position: 'absolute',
                  width: '100%',
                  bottom: '0px',
                }}
              >
                {this.props.peer.userName}
              </div>
            </div>
          </div>
          

            <video
              className={"user-video"}
              playsInline
              style={this.props.userVideoAudio[this.props.peer.peerID]
                ?  this.props.userVideoAudio[this.props.peer.peerID].screen?{position:"relative"}:{}:{}}
              autoPlay
              id={this.props.peer.extra.type === 'host' ? 'HOST_VIDEO' : ''}
              ref={this.ref}
            ></video>
            {/* {isScreenSharing && !this.props.tab ? (
              <button
                className="MinimizeButton"
                onClick={this.handleMinimizeClick}
                style={{
                  position: isScreenSharingMinimized ? 'fixed' : 'absolute',
                  top: !isScreenSharingMinimized ? '20px' : '',
                  bottom: isScreenSharingMinimized ? '250px' : '',
                  right: isScreenSharingMinimized ? '25px' : '25px',
                  border: 'none',
                  backgroundColor: 'white',
                  borderRadius: '5px'
                }}
              >
                {isScreenSharingMinimized ? (
                  <img src={max} alt="Maximize" title='Maximize' />
                ) : (
                  <img src={min} alt="Minimize" title='Minimize' />
                )}
              </button>
            ) : null} */}

        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    userVideoAudio: state.Call.userVideoAudio,
    Tab: state.Call.Tab,
  };
};

const mapDispatchToProps = { ...HostActions };

export default connect(mapStateToProps, mapDispatchToProps)(VideoCard);
