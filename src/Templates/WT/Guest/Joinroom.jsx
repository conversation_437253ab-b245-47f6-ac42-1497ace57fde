import React from "react";
// import { redirect } from "react-router-dom";
import Fire from "../../../config/Firebase.jsx";
import * as HostActions from "../../../Actions/HostAction";
import { connect } from "react-redux"
import { PostRequest } from "../../../Tools/helpers/api.js";
import { getCookie, setCookie } from "../../../Tools/helpers/domhelper.js";
import banner from '../../../assets/img/guest_joining/Banner.png'
import * as Sessions from "../../../Actions/Sessions.js";
class Joinroom extends React.Component {

  constructor(props){
    super(props);
    let params = new URL(document.location).searchParams;

    this.state={
        modal:false,
        name:'',
        job_title:'',
        number:'',
        email:'',
        start: new Date().getTime(),
        endTime: "",
        exception:'',
        info_details:false,
        bed:'',
        bath:'',
        sqft:'',
        img:'',
        loader:true,
        roomStatus:'Live',
        noroom:false,
        roomdata:false,
        GuestId:params.get("user"),
        leadId:params.get("lead_id"),
        isleadPresent:false,
        isSubmitting: false,
    }

    this.handlechange=this.handlechange.bind(this);
    this.handlejoin=this.handlejoin.bind(this);
    this.submit = this.submit.bind(this);

  }
    componentDidMount(){
    if (this.state.leadId) {
        this.setState({isleadPresent:true});
        PostRequest({
            url: process.env.REACT_APP_API_BACKEND_API_URL + 'publicapis/GetLead',
            body: {
                "session_id": this.props.id,
                "lead_id": this.state.leadId,
            }
        }).then(response => {
            localStorage.setItem(this.props.id, JSON.stringify({ GuestId: response.key, data: response }));
            this.props.SetClientData({ GuestId: response.key, data: response });
    
            const data = localStorage.getItem(this.props.id);
    
            return PostRequest({
                url: process.env.REACT_APP_API_BACKEND_API_URL + `publicapis/organization/${JSON.parse(data).data.organization_id}/project/${JSON.parse(data).data.interested_in[0].project_id}/getProject`
            });
        }).then(response => {
            setCookie("projectName", response.name);
            window.location = (`/salestool/guest/room/${ this.props.id}` )
        }).catch(error => {
            window.location = window.location.href.split("?")[0];
        });
    }
    this.setState({isleadPresent:true});
    Fire.firestore().collection("sessions").doc(this.props.id).get().then((doc)=>{
        console.log("joinroom",doc.data())
            if(doc.exists){
                if(doc.data().status=="ended"){
                    window.location = `/salestool/feedback/${this.props.id}`;
                }
                this.props.SetConfig(doc.data())
                this.setState({roomdata:doc.data()})
            }else{
                this.setState({
                    noroom:true
                })
            }
        // if(this.state.roomdata.target && this.state.roomdata.target.displayName){
        //     this.setState({name:this.state.roomdata.target.displayName})
        // if(this.state.roomdata.target.Email){
        //     this.setState({email:this.state.roomdata.target.Email})
        // }
        // if(this.state.roomdata.target.Phone){
        //     this.setState({number:this.state.roomdata.target.Phone})
        // }
        // }
        this.setState({
            loader:this.state.GuestId
        })

    window.scrollTo(0, 0);

  })
}

submit(event){
    try {
        this.setState({ isSubmitting: true });
        PostRequest({url:process.env.REACT_APP_API_BACKEND_API_URL+'publicapis/CreateLead',body:{
            "name":this.state.name,
            "email":this.state.email,
            "phone_number":this.state.number,
            "session_id":this.props.id,
            source:'sales_session',
            status:'new',
        }}).then((response)=>{
            localStorage.setItem(this.props.id,JSON.stringify({GuestId:response.key,data:response}));
            this.props.SetClientData({GuestId:response.key,data:response})
            const data = localStorage.getItem(this.props.id);


            PostRequest({url:process.env.REACT_APP_API_BACKEND_API_URL+`publicapis/organization/${JSON.parse(data).data.organization_id}/project/${JSON.parse(data).data.interested_in[0].project_id}/getProject`}).then((response)=>{
                setCookie("projectName",response.name)
                console.log("redirecting to project",response)
                this.props.SetProjectDetails(response);
                localStorage.setItem('projectDetails', JSON.stringify(response));
                window.location = (`/salestool/guest/room/${ this.props.id}` )
            })
        })
    } catch (error) {
        this.setState({ isSubmitting: false });

    }
                
            
}






handlejoin(event) {
    event.preventDefault();
    console.log(getCookie('accessToken'))
    console.log(this.state)
    
    let isValid = true;
    
    if (this.state.name === '') {
      this.showError('name', 'Name is required');
      isValid = false;
    } else if (!/^[a-zA-Z-,]+(\s{0,1}[a-zA-Z-, ])*$/.test(this.state.name)) {
      this.showError('name', 'Contains only alphabets[A-Z]');
      isValid = false;
    }
    
    if (this.state.email === '') {
      this.showError('email', 'Email is required');
      isValid = false;
    } else if (!/^\w+([.-]?\w+)*@\w+([.-]?\w+)+$/.test(this.state.email)) {
      this.showError('email', 'Email Address invalid format');
      isValid = false;
    }
    
    if (this.state.number !== '' && !/^[-+]?[0-9]+$/.test(this.state.number)) {
      this.showError('number', 'Contains only Numbers[0-9]');
      isValid = false;
    }
    
    if (isValid) {
      this.submit();
    }
  }

  showError(fieldName, message) {
    document.getElementById(fieldName).classList.add('input_error_border');
    document.getElementById(fieldName + '_error').childNodes[0].classList.remove('input_error_hide');
    document.getElementById(fieldName + '_error').childNodes[0].textContent = message;
  }

  handlechange(event) {
    this.setState({
      exception: ""
    })
    const { name, value } = event.target;

    this.setState({ [name]: value });
    document.getElementById(name).classList.remove('input_error_border');
    document.getElementById(name + '_error').childNodes[0].classList.add('input_error_hide');
  }










    render() {
            return (
                <>
                    {this.state.isleadPresent ? <div className="w-full h-screen" >
                        <div className="flex h-full w-full">
                            <div  className="bg-[#171717] flex w-full">
                                {/* Image Col */}
                                <div className="w-1/2 relative hidden sm:block ">
                                    <img alt="Background" className="w-full h-full object-cover" src={banner}></img>
                                    <div className="absolute top-5 left-4">
                                    <img alt="Logo" className="h-16 lg:h-7 w-auto" src={document.getElementById('Reseller_Whitelogo').src}></img>
                                    </div>
                                </div>

                                {/* Form Col */}
                                <div className=" relative w-full sm:w-1/2 bg-[#171717] flex flex-col justify-center items-center">
                                <div className="sm:hidden absolute top-4 left-3">
                                <img alt="Logo" className="h-16 lg:h-7 w-auto" src={document.getElementById('Reseller_Whitelogo').src}></img>

                                    </div>
                                    <div className="h-fit w-3/4 max-w-md sm:max-w-sm min-w-[300px]">

                                        <div className="w-full flex justify-center items-center">
                                        <h2 className="text-center text-neutral-100 font-bold text-base w-10/12 sm:w-11/12">You are joining a virtual tour session!!</h2>
                                        </div>
                                        {this.state.roomdata && !this.state.GuestId ?
                                            <div className="flex justify-center lg:border lg:border-[#737373] lg:rounded-xl p-3 lg:mt-2">
                                                    <form onSubmit={this.handlejoin} className="w-full mb-1" >
                                                        <div className="">
                                                            <div className="w-full h-fit flex-col justify-start items-start inline-flex  bg-inherit">
                                                                <label className="text-[#F5F5F5] text-xs font-semibold mb-2">
                                                                    Name
                                                                </label>
                                                                <input onChange={this.handlechange} style={{border:"1px solid #737373"}} type="text" id="name" name="name" className="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-[#ffffffba] placeholder:text-xs" placeholder="Name" />
                                                                <small id="name_error" className="">
                                                                    <span className="text-xs text-bold not-italic tracking-normal text-center text-[#ff4070] input_error_hide">
                                                                        <i className="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                                                        Name is <sup>*</sup> required
                                                                    </span>
                                                                </small>
                                                            </div>

                                                            <div className="w-full h-fit flex-col justify-start items-start inline-flex  bg-inherit mt-2">
                                                                <label className="text-[#F5F5F5] text-xs font-semibold mb-2">
                                                                    Email ID
                                                                </label>
                                                                <input onChange={this.handlechange} type="email" id="email" name="email"  style={{border:"1px solid #737373"}} className="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-[#ffffffba] placeholder:text-xs" placeholder="Email ID" />
                                                                <small id="email_error" className="">
                                                                    <span className="text-xs text-bold not-italic tracking-normal text-center text-[#ff4070] input_error_hide">
                                                                        <i className="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                                                        Email ID is <sup>*</sup> required
                                                                    </span>
                                                                </small>
                                                            </div>

                                                            <div className="w-full h-fit flex-col justify-start items-start inline-flex  bg-inherit mt-2">
                                                                <label className="text-[#F5F5F5] text-xs font-semibold mb-2">
                                                                    Phone number
                                                                </label>
                                                                <input onChange={this.handlechange} type="text" id="number" name="number"  style={{border:"1px solid #737373"}} className="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-[#ffffffba] placeholder:text-xs" placeholder="Phone Number" />
                                                                <small id="number_error" className="">
                                                                    <span className="text-xs text-bold not-italic tracking-normal text-center text-[#ff4070] input_error_hide">
                                                                        <i className="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                                                        Phone Number is <sup>*</sup> required
                                                                    </span>
                                                                </small>
                                                            </div>


                                                            <div id="other_exception" style={{ display: 'none' }} className="form-group">
                                                                <span className="input_error input_error_hide">
                                                                    <i className="fa fa-exclamation-circle" aria-hidden="true"></i>
                                                                    {this.state.exception}
                                                                </span>
                                                            </div>

                                                            <div className="text-center mt-3">
                                                                <button
                                                                    id="submit"
                                                                    type="submit"
                                                                    className="bg-[#1C74D0] w-full h-12 rounded-3xl lg:rounded text-base text-white"
                                                                    disabled={this.state.isSubmitting}
                                                                    style={{ cursor: this.state.isSubmitting ? "progress" : "pointer" }}
                                                                >
                                                                    {this.state.isSubmitting ? (
                                                                    <i
                                                                        id="loginloader"
                                                                        className="fa fa-circle-o-notch fa-spin"
                                                                        style={{ fontSize: "1rem", color: "white" }}
                                                                    ></i>
                                                                    ) : (
                                                                    "Join Session"
                                                                    )}
                                                                </button>
                                                            </div>


                                                        </div>
                                                    </form>
                                            </div>
                                            : <>
                                            {this.state.noroom?<div className="modal" id="admit_modal" tabIndex={-1} role="dialog" style={{display: 'block'}}>

                                                <div className="modal-dialog" role="document">
                                                <div className="modal-content">
                                                    <div className="modal-header">
                                                    <h5 style={{color: '#222b45'}} className="modal-title">Session not Found </h5>
                                                    </div>
                                                    <div className="modal-body">
                                                    <p className="share_content">Kindly re-check the invite link</p>
                                                    </div>

                                                </div>
                                                </div>
                                                </div>
                                                :<><div className="w-full h-fit flex justify-center items-center mt-1">
                                                <i id="loginloader" className="fa fa-circle-o-notch fa-spin text-3xl  sm:text-4xl  text-white " style={{paddingRight: 2, paddingLeft: 2 }}></i>
                                                </div></>}
                                            </>}
                                    </div>

                                </div>
                            </div>



                        </div>

                    </div> : <><div className="w-full h-fit flex justify-center items-center mt-1">
<i id="loginloader" className="fa fa-circle-o-notch fa-spin text-3xl  sm:text-4xl  text-white " style={{paddingRight: 2, paddingLeft: 2 }}></i>
</div></>}

                </>)
        }
}
const mapStateTothisprops = state => {
    return {
        Config: state.Call.config,
        ProjectDetails: state.Sessions.projectDetails,
    }

}

const mapDispatchTothisprops = {
    ...HostActions,
    ...Sessions,
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(Joinroom)
