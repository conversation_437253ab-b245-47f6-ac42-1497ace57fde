import React from 'react';
import { connect } from 'react-redux';
import '../../../styles/video.css';
import * as HostActions from "../../../Actions/HostAction";
import {socket,peersRef} from "../../../Actions/HostAction"
import SideBar from './SideBar';
import * as ExceptionActions from "../../../Actions/Exception"
import RealTimeControls from './RealTimeControls';
import TypeSwitch from './TypeSwitch';

var roomId;

class Room extends React.Component{
  constructor(props){
    super(props);
    this.state={
      upload:0,
      recorder:false,
      recordertime:false,
      platform:window.vuplex?false:true,
      fullscreen:true
    }
    this.Sidenav = React.createRef();
    this.PinVideo= React.createRef();
    this.SendDatabyChannel=this.SendDatabyChannel.bind(this);
    this.SendDatabyChannelByType=this.SendDatabyChannelByType.bind(this);
    this.DataChannelMessage=this.DataChannelMessage.bind(this)

    roomId = this.props.roomId;
    this.findPeer=this.findPeer.bind(this);
  }


  DataChannelMessage(Peer,data){


  }
componentDidMount(){


this.props.SetVideoDevices()
const Avatarsrc=["https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar1.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar2.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar3.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar4.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar5.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar6.png",
]
const extra={color:Math.floor(Math.random()*16777215).toString(16),username:this.props.UserName,type:"Guest",id:socket.id,Avatar:Avatarsrc[Math.floor(Math.random() * 5)]}

  window.addEventListener('beforeunload', (e) => {
    e.preventDefault();
    socket.emit('BE-leave-room', { roomId, leaver: this.props.UserName });
    sessionStorage.removeItem('user');
    window.location.href = '/';
  });
 document.getElementById("LocalVideo").srcObject = this.props.LocalStream;
 this.props.Call(this.props.LocalStream,this.DataChannelMessage);
 socket.on('FE-user-leave', ({ userId, userName }) => {
        const peerIdx = this.findPeer(userId);
        if(peerIdx){
          peerIdx.peer.destroy();
          if(peerIdx.peer.extra.type=="host")
        this.props.SetHostStatus(false)
         this.props.UpdatePeer(this.props.Peers,userId)
        }

      });
  socket.emit('BE-join-room', { roomId, userName: this.props.UserName,extra:JSON.stringify(extra), audio:this.props.Audio,video:this.props.Video,screen:false});
}







findPeer(id) {
  return peersRef.find((p) => p.peerID === id);
}
VideoControl(){
  if(this.props.CameraAccess){
    this.props.ToggleUserVideo({roomId:roomId,
      Peers:this.props.Peers,
      Audio:this.props.Audio,
      Video:this.props.Video,
      LocalStream:this.props.LocalStream})
     }
     else{
  this.props.SetModalException("Camera is not Attached")
}
}

SendDatabyChannel(Data){
  Object.values(this.props.Peers).map(Peer=>{
    if(Peer && Peer._channel){
       if(Peer._channel.readyState=="open")
    Peer.send(JSON.stringify(Data))
    }
  })
}


SendDatabyChannelByType(Data,Type=false){
  Object.values(this.props.Peers).map(Peer=>{
    if(Peer && Peer._channel){
      if(!Type){
        if(Peer._channel.readyState=="open")
        Peer.send(JSON.stringify(Data))
      }else{
        if(Peer.extra.type==Type)
        if(Peer._channel.readyState=="open")
        Peer.send(JSON.stringify(Data))
      }
    }
  })
}

toHumanReadableTime(isoString) {
  const date = new Date(isoString);

  const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;

  return formattedDate;
}

render(){


  return (

<>
<div style={{ position: "relative", flex: "auto", height: "100%", display: "flex", flexDirection: "column" }} id="MainContentDiv">
          <div style={{ width: '100%', display: 'flex', flexDirection: 'row', flex: 'auto',position:"relative" ,overflow:"hidden",height:"100%"}}   >
            <div className="Propvr_Embed" id="Propvr_Embed" style={{ "position": "relative", height: "100%", flex: "auto" }}>
              <TypeSwitch platform={this.state.platform} SendDatabyChannelByType={this.SendDatabyChannelByType} SendDatabyChannel={this.SendDatabyChannel}   SubscribeToCustom={HostActions.SubscribeToCustom} SendCustomMessage={this.props.SendCustomMessage} roomId={this.props.roomId}/>
            </div>
            {
            this.state.upload?<div className="modal" id="admit_modal" tabIndex={-1} role="dialog" style={{ display: 'block' }}>
            <div className="modal-dialog" role="document">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 style={{ color: '#222b45' }} className="modal-title">Uploading the recorded Content</h5>
                </div>

                <div className="modal-body">
                <p>Don't close the tab until it gets uploaded</p>
                <p className="share_content"><div className="progress skeleton-box" style={{width:"100%",height:"15px"}}>
                  <div className="progress-bar"  role="progressbar" style={{width: this.state.upload+"%",backgroundColor:"var(--main-color)",height:"15px"}} aria-valuenow={25} aria-valuemin={0} aria-valuemax={100} />
                </div>
                <span style={{float: 'right', flex: 'auto', maxWidth: 'fit-content', marginLeft:"8px",marginTop:"8px"}}>{this.state.upload.toFixed(0)+"%"}</span></p>

              </div>
              </div>
            </div>
          </div>:""
          }

<SideBar sendmessage={this.sendmessage} PinVideo={this.PinVideo} socketid={socket.id} roomId={this.props.roomId}  Sidenav={this.Sidenav}/>
          </div>
         <RealTimeControls roomId={this.props.roomId}/>

          </div>

       </>
  );}
};



const mapStateTothisprops = state => {
  return {
    Peers: state.Call.peers,
    VideoDevices:state.Call.VideoDevices,
    SocketId:state.Call.SocketId,
    LocalStream:state.Call.LocalStream,
    UserName:state.Call.UserName,
    userVideoAudio:state.Call.userVideoAudio,
    Video:state.Call.Video,
    Audio:state.Call.Audio,

  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(Room)