import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions'
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";

class AleGuest extends Component {
    constructor(props){
        super(props);
        this.state = {
            ale_link:""
        }
    }
  componentDidMount() {
    this.props.SubscribeToCustom((msg) => {
      var data = JSON.parse(msg);
      var contentWindow  = document.getElementById('showcase_frame').contentWindow
      contentWindow.postMessage({...data,simulate:true},"*")
    })
    Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({ includeMetadataChanges: true }, (doc) => {
      const config_data = doc.data();
      // const tempLink = config_data.ale_link.replace('https://propvr-ui-library-k5aul2eu6q-uc.a.run.app', 'http://localhost:5173'); -------------->for testing in local
      let ale_link_with_guest_param;
      if (config_data.ale_link.includes('?')) {
        ale_link_with_guest_param = config_data.ale_link + '&salestoolGuest=true';
      } else {
        ale_link_with_guest_param = config_data.ale_link + '?salestoolGuest=true';
      }
      console.log("link with guest", ale_link_with_guest_param)
      this.setState({
        ale_link: ale_link_with_guest_param
      });
    });


  }
  

  render() {
    if( this.state.ale_link){
        return (
            // <div style={{ width: "100%", height: "100%", position: "relative", overflow: "hidden" }}>
              <iframe
              id="showcase_frame"
                title="Damac London Scene"
                src={this.state.ale_link}
                style={{ width: "100%", height: "100%", position: "absolute" }}
              />
            // </div>
          );

    }else{
        return (
          <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-50 flex items-center justify-center">
          <div className="bg-black/40 backdrop-blur-md p-8 rounded-xl shadow-lg text-center">
            <div className="lds-ring mb-4">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
            <h2 className="text-xl font-bold text-white mb-2">
              Preparing Your Virtual Experience
            </h2>
            <p className="text-sm text-gray-300">
              Just a moment while we set the stage for your immersive journey...
            </p>
          </div>
          <style jsx>{`
            .lds-ring {
              display: inline-block;
              position: relative;
              width: 80px;
              height: 80px;
            }
            .lds-ring div {
              box-sizing: border-box;
              display: block;
              position: absolute;
              width: 64px;
              height: 64px;
              margin: 8px;
              border: 4px solid #FFFFFF;
              border-radius: 50%;
              animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
              border-color: #FFFFFF transparent transparent transparent;
            }
            .lds-ring div:nth-child(1) {
              animation-delay: -0.45s;
            }
            .lds-ring div:nth-child(2) {
              animation-delay: -0.3s;
            }
            .lds-ring div:nth-child(3) {
              animation-delay: -0.15s;
            }
            @keyframes lds-ring {
              0% {
                transform: rotate(0deg);
              }
              100% {
                transform: rotate(360deg);
              }
            }
          `}</style>
        </div>
        )
    }

  }
}
const mapStateToProps = state => {
    return {
      configDetails: state.Sessions.configData,
    }
  }
const mapDispatchTothisprops = {
    ...Sessions
}
export default connect(mapStateToProps, mapDispatchTothisprops)(AleGuest);