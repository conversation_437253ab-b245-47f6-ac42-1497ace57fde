import React from "react";
import { connect } from "react-redux";


class Timer extends React.Component{

    constructor(props)
    {
        super(props)
        this.state = {
            start:new Date(this.props.start),
            timer:0,
            sleepmodal:false,
            sleeptimer:0,
            timeoutmodal:false
        }

    }
    componentDidMount(){
        const job = setInterval(() => {
            const time = new Date().getTime();
            const starttime = this.state.start.getTime();
            const diff = time-starttime;
            if(this.props.trackMaxtimeout && !this.state.timeoutmodal){
                if(diff >= ((((this.maxsessionTime*60)-10)*1000))){
                    if(this.state.sleepmodal){
                        this.StopSleep()
                    }
                    this.setState({
                        timeoutmodal:true,
                        sleeptimer:10,
                    })
                    this.StartSleep()
                }
            }
            this.setState({timer:diff})
        }, 1000);
    }
    convertTimetohours(diff){
        let seconds = diff/1000;
        let minutes = seconds/60;
        let hours = minutes/60;

        let tallyseconds = parseInt(seconds%60);
        let tallyminutes = parseInt(minutes);
        if(tallyminutes<=9)
            tallyminutes = "0"+tallyminutes
        if(tallyseconds<=9)
            tallyseconds = "0"+tallyseconds
        if(hours>0){
        return (tallyminutes+":"+tallyseconds)
        }
        return (tallyminutes+":"+tallyseconds)
    }
    

    render(){
        return(
            <>
                {this.convertTimetohours(this.state.timer) !== undefined ? 
    <div style={{display:this.props.ShowControls ? "flex" : "none"}} className="flex text-white shadow-[0px_17px_21px_0px_#0000000D] text-[larger] px-[12px] py-[6px] left-2 sm:top-2 bg-opacity-10 TimerControl z-10 h-auto">
        <span className="ml-[0px] mr-[5px] mt-[5px] mb-[5px] w-[15px]" style={{zIndex:'99999'}}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="8" cy="8" r="8" fill="#00C851" />
            </svg>
        </span>
        {this.state.timer < 0 ? "00:00" : this.convertTimetohours(this.state.timer)}
    </div> 
: 
    <></>
}




            </>
        )
    }
}
const mapStateToProps = state => {
    return {

        ShowControls:state.Call.ShowControls,
    }
  }


  export default connect(mapStateToProps, null)(Timer)