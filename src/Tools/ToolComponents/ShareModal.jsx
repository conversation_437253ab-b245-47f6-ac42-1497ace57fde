import React from "react";
// import { Redirect, Route, Link } from "react-router-dom";
import {connect}  from "react-redux";
import 'jquery';

class Share extends React.Component {
  constructor(props){
    super(props);
    this.state={
        url:'',
        tooltip:false
    }
   this.handlecopy=this.handlecopy.bind(this);
   this.onClick =  this.onClick.bind(this);

}

onClick(event){
  this.setState({
    tooltip:false
  })
  this.props.open_close('share',false);
}
handlecopy(event){
  navigator.clipboard.writeText(encodeURI(this.state.url));
  this.setState({
    tooltip:true
  })
}
componentDidMount(){

    window.scrollTo(0, 0);

    this.setState({

        url:window.location.protocol+'//'+window.location.hostname+(window.location.port.length?":"+window.location.port:"")+"/salestool/joinroom/"+this.props.roomId

    })
  }













  render() {
      return(
      <div style={{display:this.props.share===true?'block':'none'}} className="bg-[black]/30 bg-opacity-40 backdrop-blur fixed z-[10501] hidden overflow-hidden inset-0 p-4 h-full w-full" id="share_modal" tabIndex="-1" role="dialog">
      <div className="px-3 sm:mx-auto flex justify-center items-center h-full w-full sm:py-7" role="document">
        <div className={" max-w-full sm:max-w-lg h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-3xl m-auto rounded inset-0 sm:shadow "+(document.title!="Prop VR"?" pb-4":"") }>
          <div className="flex items-start justify-between p-4 rounded-t-[0.3rem]">
            <h5 className="text-lg font-[bold] not-italic tracking-[normal] text-white mb-0 font-sans">Add others to the session</h5>
            <button onClick={this.onClick} type="button" className="w-8 h-8 rounded-full bg-[black]/30 bg-opacity-90 flex justify-center items-center" data-dismiss="modal" aria-label="Close">


            <span aria-hidden="true">
                  <svg  className="w-6 h-6 text-white fill-white" viewBox="0 0 24 24">
                  <defs>
                      <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z"/>
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(6 6)">
                      <use fill="white" href="#prefix__close"/>
                  </g>
              </svg></span>
            </button>
          </div>

          <div className="relative flex-auto pt-6 pb-[5px] px-6 py-1 px-6">
            <p className="text-sm mt-0 mb-2.5 mx-0 text-white">Share the link with people you want to invite to the session</p>
             <span className="break-words text-base font-sans font-[normal] not-italic tracking-[normal] text-white">{encodeURI(this.state.url)}</span>
          </div>
          <div className={"flex items-center justify-start pt-0 pb-[5px] px-6"} style={{padding:"0px 24px 5px"}}>
            <span>
              <svg  className="w-6 h-6 fill-white text-white" viewBox="0 0 20 20">
                  <defs>
                      <path id="prefix__copy" d="M8 7.167c-.46 0-.833.374-.833.833v5c0 .46.374.833.833.833h5c.46 0 .833-.374.833-.833V8c0-.46-.373-.833-.833-.833H8zM8.277.5c1.226 0 2.223.997 2.223 2.223V5.5H13c1.378 0 2.5 1.122 2.5 2.5v5c0 1.378-1.122 2.5-2.5 2.5H8c-1.378 0-2.5-1.122-2.5-2.5v-2.5H2.723C1.497 10.5.5 9.503.5 8.277V2.723C.5 1.497 1.497.5 2.723.5h5.554zm0 1.667H2.723c-.307 0-.556.249-.556.556v5.554c0 .307.249.556.556.556H5.5V8c0-1.378 1.122-2.5 2.5-2.5h.833V2.723c0-.307-.249-.556-.556-.556z"/>
                  </defs>
                  <g fill="none" fillRule="evenodd" transform="translate(2 2)">
                      <use className="fill-white" href="#prefix__copy"/>
                  </g>
              </svg>

            </span>
            <span onClick={this.handlecopy} data-toggle="tooltip" data-placement="right" data-tip="Joining link copied"  className="text-sm font-bold not-italic tracking-[normal] text-center text-white cursor-pointer">Copy joining link</span>

            <div style={{display:this.state.tooltip?'block':'none'}} className="share_tooltip">Joining link copied</div>
            
          </div>
          {document.title=="Prop VR"?<div className="relative flex-auto pt-6 pb-[5px] px-6 text-white" style={{ padding: "2px 24px 24px 24px" }}>
                <div style={{paddingBottom:"4px"}}>
                  Visit : <strong>propvr.tech/join</strong>
                </div>
                <span className="text-sm mt-0 mb-2.5 mx-0 text-white">
                  Meeting Code:
    <strong style={{ padding: 12, letterSpacing: 7 }}>{this.props.Config?this.props.Config.Code:""}</strong>
                </span>
              </div>:""}
        </div>
      </div>
      </div>)
  }
}
const mapStateTothisprops = state => {
  return {
    Config: state.Call.config,

  }
}



export default connect(mapStateTothisprops, null)(Share)
