import React from "react"
import {connect} from "react-redux"
class Toast extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      notification: []
    }
    
  }




 

  render() {
    return (<>
    {Object.keys(this.props.Toasts).length?
      <div className="toaster">

        {Object.values(this.props.Toasts).map((nodes) => {
          return (
            <>
              <div className="h-fit mb-2 rounded-none inset-0 flex flex-col w-full sm:w-80 pointer-events-auto bg-[black]/30 bg-opacity-40 sm:rounded backdrop-blur-xl p-4 relative shadow-md transition-all duration-500">
              
                <div className="flex items-center justify-start  rounded-t-[0.3rem]">
                
                <svg className="w-6 h-6 fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="info"><rect  transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/><circle cx="12" cy="8" r="1"/><path d="M12 10a1 1 0 0 0-1 1v5a1 1 0 0 0 2 0v-5a1 1 0 0 0-1-1z"/></g></g></svg>
                  <h4 className="ml-2 text-base font-semibold not-italic tracking-[normal] text-white text-[#222b45] my-0 font-[Open_Sans]">
                  {nodes.message}</h4>
                </div>
                {nodes.postmessage?<div className="relative flex-auto pt-2">
                  <p className="text-sm font-[normal] not-italic m-0 tracking-[normal] text-white font-sans border-t border-t-solid border-t-white">{nodes.postmessage}</p>
                </div>:<></>}

    
              </div>

            </>
          )
        })}


      </div>:<></>}
      </>
    )
  }
}
const mapStateToProps = state => {
    return { 
        Toasts:state.Exception.toasts
    };
  };
  // const mapDispatchToProps = {
  // ...Actions
  // }
  
  export default connect(mapStateToProps,null)(Toast);

