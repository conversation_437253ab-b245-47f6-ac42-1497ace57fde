import React from "react";
import Fire from "../config/Firebase.jsx";
import banner from "../assets/img/guest_joining/Banner.png"
export default class EndSession extends React.Component {


  constructor(props) {
    super(props);
    this.state = {
      stars: 0,
      qn1: true,
      qn2: false,
      ans: "Need to explore further",
      cmnt: "",
      feedback: false,
      pid: this.props.roomId,
      info_det: false,
      bed: '',
      bath: '',
      sqft: '',
      img_src: '',
      project: '',
      loader: true,
      Config: false
    }
    this.handlechange = this.handlechange.bind(this);
    this.changeRating = this.changeRating.bind(this);

  }

  componentDidMount() {
    window.scrollTo(0, 0);
    Fire.database().ref("roomsessionuat/" + this.props.roomId + "/Config").on("value", Config => {
      let uid = Config.val().Uid
      let pid = Config.val().Project
      this.setState({ Config: Config.val() })
      Fire.database().ref("users/" + uid + "/Projects/" + pid + "/thumb").once("value", child => {
        this.setState({
          project: pid,
          img_src: child.val(),
          loader: false
        })
        Fire.database().ref("users/" + uid + "/Projects/" + pid + '/Info').once("value", snap => {

          if (snap.exists()) {

            if (snap.val().baths === "" || snap.val().beds === '' || snap.val().sqft === '') {
              this.setState({
                info_det: false
              })
            } else {
              this.setState({
                bed: snap.val().beds,
                bath: snap.val().baths,
                sqft: snap.val().sqft,
                info_det: true
              })
            }
          } else {
            this.setState({
              info_det: false
            })
          }
        })

      })
    })


  }

  changeRating(newRating, name) {
    this.setState({
      stars: newRating
    });

  }

  handlechange(event) {
    this.setState({ cmnt: event.target.value })
  }

  cancel = () => {
    // localStorage.removeItem('uid');
    // localStorage.removeItem('pid');
    // localStorage.removeItem('rid');
    // localStorage.removeItem('guestkey');
    this.setState({ feedback: true })
  }

  submitFeedback = (event) => {
    event.preventDefault();
    if (this.state.Config) {

    }
    this.setState({ feedback: true })

  }

  render() {
    const screenheight = window.innerHeight;
    if (!this.state.feedback) {
      return (
        <div className="flex justify-center items-center h-screen bg-[#171717]">
          {/* Image Column */}
          <div className="hidden sm:block w-1/2">
            <img
              alt="Background"
              className="w-full h-full object-cover"
              src={banner}
            />
          </div>
      
          {/* Form Column */}
          <div className="w-full sm:w-1/2 bg-[#1F1F1F] flex flex-col justify-center items-center p-8 rounded-md shadow-lg">
            <div className="w-10/12 max-w-full min-w-[340px] text-white text-sm text-center">
              <div className="relative top-4 left-3 mb-8">
                <img
                  alt="Logo"
                  className="h-20 w-auto"
                  src={document.getElementById("Reseller_Whitelogo").src}
                />
              <h2 className="font-bold text-lg mb-4 mt-0">Your session has ended</h2>
              </div>
              <p className={`text-base mt-0 ${screenheight < 485 ? "mb-4" : "mb-8"}`}>
                Thanks for participating.
              </p>
              
            </div>
          </div>
        </div>
      );
      
    }
    else {
      return (
        <div className="flex justify-center items-center w-full h-screen bg-[#171717] ">
                      <h2 className="text-lg text-center sm:text-2xl  text-white mx-4 my-0" >Thanks for your feedback! We will get in touch.</h2>
        </div>

      )
    }
  }
}

