import Fire from "../config/Firebase"
import { getCookie, setCookie } from "../Tools/helpers/domhelper";
import { PostRequestWithHeaders } from "../Tools/helpers/api";

export function CheckAuth() {
    return dispatch => {
        Fire.auth().onAuthStateChanged((user) => {
            if (user) {
                setCookie("accessToken",user.multiFactor.user.accessToken,2)
                PostRequestWithHeaders({url:process.env.REACT_APP_API_BACKEND_API_URL+'user/GetUserDetails',body:{uid:user.uid}}).then(response=>{
                if(!getCookie("organization")){
                    setCookie("organization",response.organization_id[0])
                }
                dispatch({
                            type: "AUTH_VALIDATE", payload: {
                                Auth: user,
                               ...response
                            }
                        })
                })
                // Get_Profile(user.uid).then(child=>{
                //
                // })
            } else {
                window.location="/login"
                dispatch({
                    type: "AUTH_VALIDATE", payload: {
                        Auth: false,
                    }
                })
            }
        });
        Fire.auth().onIdTokenChanged(function(user){
            setCookie("accessToken", user.multiFactor.user.accessToken, 2)
        })
    }
}

export function SetOnlineStatus(status){
    return dispatch=>{
        dispatch({
            type: "ONLINE_STATUS", payload: status
        })
    }
}
export function Signout() {
    return dispatch => {

            Fire.auth().signOut().then(e=> {
                dispatch({
                    type: "AUTH_VALIDATE", payload: false
                })

                dispatch({
                    type: "PLAN_VALIDATE", payload: false
                })
                window.location="/login"
            }).catch(function (error) {
              alert(error.message);
            });

    }
}


export function Set_ProfilePic(payload){
    return dispatch=>{
        dispatch({type:"SET_PROFILE_DETAILS",payload})
    }
}

// function Get_Payment(uid){
// return new Promise ((resolve, reject)=>{
// Fire.database().ref("users/"+uid+"/salestool_payment").on("value",Payment=>{
//     Fire.database().ref("users/"+uid+"/userAdmin").once("value",Admin=>{
// resolve({Payment:Payment.val(),Admin:Admin.val()})
//     })
// })
// })
// }

// function Get_Profile(uid){
//     return new Promise ((resolve, reject)=>{
//         Fire.database().ref("users/"+uid+"/username").once("value",username=>{
//             Fire.database().ref("users/"+uid+"/profile_pic").once("value",profile_pic=>{
//                     resolve({username:username.val(),profile_pic:profile_pic.val()||undefined})
//     })
//         })
//         })
// }

